import { createGraphClient } from "../utilities/graph";
import { logger } from "../utilities/log";
import {
  createCredential,
  processUsersChannelsDataBatch
} from "./impl";
import { validateTableStorageConnection, fetchUsersChatsData } from "../utilities/tableStorage";
import { validateCosmosDBConnection } from "../utilities/cosmos";
import {
  IUsersChannelsTableStorageData,
  IModifiedUsersChannelsData,
} from '../utilities/models';
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  logger.log("========== PHASE 1 ==========");
  logger.log("--- Creating Graph Client ---");
  const credential = createCredential(
    process.env["AzureTenantId"] ?? "",
    process.env["AzureClientId"] ?? "",
    process.env["AzureClientSecret"] ?? ""
  );
  if (!credential) {
    logger.error("[Index:CreateCredential] NO_CREDENTIALS");
    return Promise.reject(new Error("[Index:CreateCredential] NO_CREDENTIALS"));
  }
  const graphClient = createGraphClient(credential);

  logger.log("========== PHASE 2 ==========");
  logger.log("--- Validate Table Storage Connection ---");
  try {
    await validateTableStorageConnection(logger);
    logger.log("[Index:TableStorageConnection] Successfully Connected to Table Storage.");
  } catch (error) {
    logger.log(`[Index:TableStorageConnection] TableStorage Connection Failed: ${error}`);
    throw error;
  }

  logger.log("========== PHASE 3 ==========");
  logger.log("--- Get Table Storage Users Chats ---");

  // Read batch configuration from .env
  const CHANNEL_TABLE_STORAGE_BATCH_NUMBER = parseInt(process.env.CHANNEL_TABLE_STORAGE_BATCH_NUMBER || '2');
  const CHANNEL_TABLE_STORAGE_BATCH_SIZE = parseInt(process.env.CHANNEL_TABLE_STORAGE_BATCH_SIZE || '2');
  logger.log(`[Index:TableStorage] Running Batch ${CHANNEL_TABLE_STORAGE_BATCH_NUMBER} with Size ${CHANNEL_TABLE_STORAGE_BATCH_SIZE}`);

  let usersChannelsTableStorageData: IUsersChannelsTableStorageData[] = [];
  let modifiedUsersChannelsTableStorageData: IModifiedUsersChannelsData[] = [];
  try {
    const usersChannelsTableStorageResults = await fetchUsersChatsData(logger);
    logger.log(`[Index:TableStorage] Successfully Retrieved ${usersChannelsTableStorageResults.length} Users Chats Data from Table Storage.`);

    const allUsersChatsData = usersChannelsTableStorageResults.map((data, index) => ({
      countId: index + 1, 
      userId: data.partitionKey,
      teamId: data.teamId,
      channelId: data.rowKey,
      chatType: data.chatType,
      status: data.status
    }));

    // CHANNEL_TABLE_STORAGE_BATCH_NUMBER = 1
    // CHANNEL_TABLE_STORAGE_BATCH_SIZE = 100

    // startRange= (1 - 1) * 100 + 1
                    // = 0 * 100 + 1
                    // = 0 + 1 
                    // = 1
    const startRange = (CHANNEL_TABLE_STORAGE_BATCH_NUMBER - 1) * CHANNEL_TABLE_STORAGE_BATCH_SIZE + 1;
    // endRange = 1 * 100 = 100
    const endRange = CHANNEL_TABLE_STORAGE_BATCH_NUMBER * CHANNEL_TABLE_STORAGE_BATCH_SIZE;
    
    usersChannelsTableStorageData = allUsersChatsData.filter(chat => 
      chat.countId >= startRange && chat.countId <= endRange
    );
    
    logger.log(`[Index:TableStorage] Filtered for Batch: ${CHANNEL_TABLE_STORAGE_BATCH_NUMBER} | Batch Size: ${startRange}-${endRange} (${usersChannelsTableStorageData.length} records)`);

    if (usersChannelsTableStorageData.length > 0) {
      logger.log(`\n\n[Index:TableStorage] usersChannelsTableStorageData: ${JSON.stringify(usersChannelsTableStorageData)}`); // !!!
    }

    // Adjust Filter
    const filterUsersChatsData = usersChannelsTableStorageData.filter(chat => 
        chat.chatType === 'channel' 
    );
    logger.log(`[Index:TableStorage] Filtered ${filterUsersChatsData.length} Records from usersChannelsTableStorageData: ${usersChannelsTableStorageData.length} Total Records`);

    const chatUsersMap = new Map<string, any[]>();
    for (const chatDetail of filterUsersChatsData) {
      const chatIdVal = chatDetail.channelId;
      if (chatIdVal) {
        if (!chatUsersMap.has(chatIdVal)) {
          chatUsersMap.set(chatIdVal, []);
        }
        chatUsersMap.get(chatIdVal)!.push(chatDetail);
      }
    }

    // Create modifiedUsersChannelsTableStorageData
    modifiedUsersChannelsTableStorageData = Array.from(chatUsersMap.entries()).map(([channelId, users], index) => ({
      countId: index + 1,
      teamId: users[0].teamId,
      channelId: users[0].channelId,
      chatType: users[0].chatType,
      users: users.map(user => ({
        id: user.userId,
        status: user.status
      })),
      userCount: users.length
    }));

    logger.log(`\n\n[Index:TableStorage] modifiedUsersChannelsTableStorageData: ${JSON.stringify(modifiedUsersChannelsTableStorageData)}`); // !!!
    logger.log(`[Index:TableStorage] Converted ${usersChannelsTableStorageData.length} usersChannelsTableStorageData into ${modifiedUsersChannelsTableStorageData.length} modifiedUsersChannelsTableStorageData`);

  } catch (error) {
    logger.log(`[Index:TableStorage] Failed to Retrieved Pending Chats: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 4 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:CosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:CosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }
  
  logger.log('========== PHASE 5 ==========');
  logger.log("--- Process Users Channels ---");
  try {
    await processUsersChannelsDataBatch(logger, graphClient, modifiedUsersChannelsTableStorageData);
    logger.log("[Index:ProcessUsersChannelsBatch] Successfully Proccessed Users Channels Messages.");
  } catch (error) {
    logger.error(error);
  }

  logger.log("\n========== PHASE 6 ==========");
  logger.log("--- Finish ---");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});