import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsChannelsMessages } from "./models";
import { CustomLogger } from "./log";
import { updateTableStorageChatsStatus } from "./tableStorage";

import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDB<PERSON>ey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertChannelsMessages(
  logger: CustomLogger,
  dataChatsMessages: IBatchResponseData[]
): Promise<void> {
  
  logger.log(`[CosmosDB:insertChannelsMembers] Total dataChatsMessages to Process: ${dataChatsMessages.length}`);
  // logger.log(`[CosmosDB:insertChannelsMessages] dataChatsMessages: ${JSON.stringify(dataChatsMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertChannelsMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataChatsMessages: ITeamsChannelsMessages[][] = dataChatsMessages
      .map((chatsMessages) => chatsMessages.body?.value || [])
      .filter(Array.isArray);

    const { insertedCount, restoredCount } = await processChannelsMessages(container, modifiedDataChatsMessages, logger);

    logger.log(`[CosmosDB:insertChannelsMessages] Inserted Messages: ${insertedCount}, Restored Messages: ${restoredCount}`);
    const totalMessageCount = modifiedDataChatsMessages.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:insertChannelsMessages] Skipped Existing Messages: ${totalMessageCount - insertedCount}`);

  } catch (error) {
    logger.log(`[CosmosDB:insertChannelsMessages] Error Processing Messages: ${error}`);
    throw error;
  }
}

async function processChannelsMessages(
  container: Container,
  modifiedDataChatsMessages: ITeamsChannelsMessages[][],
  logger: CustomLogger
): Promise<{ insertedCount: number; restoredCount: number }> {
  let insertedCount = 0;
  let restoredCount = 0;

  logger.log(`[CosmosDB:processChannelsMessages] Total modifiedDataChatsMessages to Process: ${modifiedDataChatsMessages.length}`);
  // logger.log(`[CosmosDB:processChannelsMessages] modifiedDataChatsMessages: ${JSON.stringify(modifiedDataChatsMessages)}`); // !!!

  for (const messageArray of modifiedDataChatsMessages) {
    for (const chatsMessages of messageArray) {
      if (!chatsMessages.id) {
        logger.log(`[CosmosDB:processChannelsMessages] Error: Missing ID for chatsMessages: ${JSON.stringify(chatsMessages.id)}`);
        continue;
      }

      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: chatsMessages.id }]
      };

      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();

        if (existingMessages.length === 0) {
          await container.items.create(chatsMessages);
          insertedCount++;
          // logger.log(`[CosmosDB:processChannelsMessages] Created New Message: ${chatsMessages.id}`);

        } else {
          // Check if the message has deletedDateTime not null and softDelete is true
          const existingMessage = existingMessages[0];

          if (existingMessage.deletedDateTime !== null && existingMessage.softDelete === true) { 
            existingMessage.lastModifiedDateTime = chatsMessages.lastModifiedDateTime;
            existingMessage.lastEditedDateTime = chatsMessages.lastEditedDateTime;
            existingMessage.deletedDateTime = chatsMessages.deletedDateTime;
            existingMessage.softDelete = chatsMessages.softDelete;
            existingMessage.body.content = chatsMessages.body?.content || "";

            await container.items.upsert(existingMessage);
            restoredCount++;
            // logger.log(`[CosmosDB:processChannelsMessages] Message: ${chatsMessages.id} - Restored Message`);

          } else {
            // Message exists but doesn't meet update conditions
            // logger.log(`[CosmosDB:processChannelsMessages] Message: ${chatsMessages.id} - Already Exists`);
          }
        }

      } catch (error) {
        logger.log(`[CosmosDB:processChannelsMessages] Error Processing Message: ${error}`);
      }
    }
  }
  return { insertedCount, restoredCount };
}

export async function insertChannelsMembers(
  logger: CustomLogger,
  dataChatMembers: IBatchResponseData[]
): Promise<void> {
  logger.log(`[CosmosDB:insertChannelsMembers] Total dataChatMembers to Process: ${dataChatMembers.length}`);
  // logger.log(`[CosmosDB:insertChannelsMembers] dataChatMembers: ${JSON.stringify(dataChatMembers)}`); // !!!

  try {
    if (!dataChatMembers || dataChatMembers.length === 0) {
      logger.log(`[CosmosDB:insertChannelsMembers] No CHATS_MEMBERS data to process`);
      return;
    }
    
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertChannelsMembers] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);
    
    const modifiedDataChatsMembers = dataChatMembers
      .filter(chat => chat.id && chat.body?.value)
      .map(chat => ({
        channelId: chat.id,
        members: chat.body?.value || []
      }));
    // logger.log(`[CosmosDB:insertChannelsMembers] modifiedDataChatsMembers: ${JSON.stringify(modifiedDataChatsMembers)}`);

    const updatedMessagesCount = await processChannelsMembers(container, modifiedDataChatsMembers, logger);
    logger.log(`[CosmosDB:insertChannelsMembers] Successfully Updated: ${updatedMessagesCount} Messages in CosmosDB`);
  
  } catch (error) {
    logger.log(`[CosmosDB:insertChannelsMembers] Error processing CHATS_MEMBERS: ${error}`);
    throw error;
  }
}

async function processChannelsMembers(
  container: Container,
  modifiedDataChatsMembers: any[],
  logger: CustomLogger
): Promise<number> {
  let totalUpdatedCount = 0;
  
  logger.log(`[CosmosDB:processChannelsMembers] Total modifiedDataChatsMembers to Process: ${modifiedDataChatsMembers.length}`);

  for (const chatEntry of modifiedDataChatsMembers) {
    const channelId = chatEntry.channelId;
    const members = chatEntry.members;

    if (!members || !Array.isArray(members) || members.length === 0) {
      logger.log(`[CosmosDB:processChannelsMembers] Skipping chat ${channelId} - No Valid Members`);
      continue;
    }
    
    try {
      // Query Chat Messages
      const querySpec = {
        query: "SELECT * FROM c WHERE c.channelIdentity.channelId = @channelId",
        parameters: [{ name: "@channelId", value: channelId }]
      };
      
      const { resources: allMessagesOfChannelID } = await container.items.query(querySpec).fetchAll();
      logger.log(`[CosmosDB:processChannelsMembers] Found ${allMessagesOfChannelID.length} Messages for channelId: ${channelId}`);
      // logger.log(`[CosmosDB:processChannelsMembers] allMessagesOfChannelID: ${JSON.stringify(allMessagesOfChannelID)}`); // !!!

      if (allMessagesOfChannelID.length === 0) {
        logger.log(`[CosmosDB:processChannelsMembers] No Messages Found for channelId: ${channelId}`);
        continue;
      }
      
      let chatUpdatedCount = 0;
      for (const chatMessages of allMessagesOfChannelID) {
        // logger.log(`[CosmosDB:processChannelsMembers] chatMessages ${JSON.stringify(chatMessages)}`);
        const wasUpdated = await processChannelsMessagesMembers(chatMessages, members, container, logger);
        if (wasUpdated) {
          chatUpdatedCount++;
          totalUpdatedCount++;
        }
      }
      logger.log(`[CosmosDB:processChannelsMembers] Completed: ${chatUpdatedCount} of ${allMessagesOfChannelID.length} Messages - Members Updated`);
      allMessagesOfChannelID.length = 0;

      // // Update Table Storage - lastCheckedDatetime
      // await updateTableStorageChatsStatus(logger, chatEntry);
      
    } catch (error) {
      logger.log(`[CosmosDB:processChannelsMembers] Error Processing channelId ${channelId}: ${error}`);
    }
  }
  return totalUpdatedCount;
}

async function processChannelsMessagesMembers(
  chatMessages: any, 
  validMembers: any[], 
  container: Container, 
  logger: CustomLogger
): Promise<boolean> {
  const messageLastModifiedDatetime = chatMessages.lastModifiedDateTime;
  if (!messageLastModifiedDatetime) {
    logger.log(`[CosmosDB:processChannelsMessagesMembers] Skipping chatMessages: ${chatMessages.id} - Missing lastModifiedDateTime`);
    return false;
  }
  
  const messageDate = new Date(messageLastModifiedDatetime);
  if (isNaN(messageDate.getTime())) {
    logger.log(`[CosmosDB:processChannelsMessagesMembers] Skipping chatMessages: ${chatMessages.id} - Invalid Date: ${messageLastModifiedDatetime}`);
    return false;
  }

  // Get existing security_user_id from cosmosDB
  let existingSecurityUserIds = chatMessages.security_user_id || [];
  let addedCount = 0;

  // Compare members visibilityHistoryStartDateTime with chatsMessages lastModifiedDateTime
  if (validMembers && Array.isArray(validMembers) && validMembers.length > 0) {
    const eligibleUserIds = checkUserVisibilityHistory(validMembers, messageDate);
    
    for (const userId of eligibleUserIds) {
      if (!existingSecurityUserIds.includes(userId)) {
        existingSecurityUserIds.push(userId);
        addedCount++;
      }
    }
  }

  if (addedCount === 0) {
    // logger.log(`[CosmosDB:processChannelsMessagesMembers] No changes needed for Message: ${chatMessages.id} - No valid members to add`);
    return false;
  }

  try {
    chatMessages.security_user_id = [...new Set(existingSecurityUserIds)];
    await container.items.upsert(chatMessages);
    
    // logger.log(`[CosmosDB:processChannelsMessagesMembers] Updated Message: ${chatMessages.id} - Added: ${addedCount} Members, Total: ${chatMessages.security_user_id.length} Members`);
    return true;
    
  } catch (error) {
    logger.log(`[CosmosDB:processChannelsMessagesMembers] Failed to Update Message: ${chatMessages.id} - ${error}`);
    return false;
  }
}

function checkUserVisibilityHistory(members: any[], messageDate: Date): string[] {
  return members
    .filter(member => {
      if (!member.userId) return false;
      
      const memberJoinDate = new Date(member.visibleHistoryStartDateTime || "0001-01-01T00:00:00Z");
      if (isNaN(memberJoinDate.getTime())) return false;
      
      return messageDate >= memberJoinDate;
    })
    .map(member => member.userId);
}