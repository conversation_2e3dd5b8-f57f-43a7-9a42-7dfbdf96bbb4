import * as dotenv from 'dotenv';

dotenv.config();

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

const CHAT_SEARCH_SIZE: string = process.env['CHAT_SEARCH_SIZE'] ?? '50';
const CHAT_MAX_MONTHS: string = process.env['CHAT_MAX_MONTHS'] ?? '24';
const CHAT_DATETIME_SCHEDULE: string = process.env['CHAT_DATETIME_SCHEDULE'] ?? 'Daytime';

function getNighttimeRollingMonthDateRange(): { start: string; end: string } {
  const today = new Date();
  
  const maxMonths = parseInt(CHAT_MAX_MONTHS, 10);
  const monthsToSubtract = isNaN(maxMonths) || maxMonths <= 0 ? 1 : maxMonths;
  
  const startDate = new Date(today);
  startDate.setMonth(today.getMonth() - monthsToSubtract);
  
  const startStr = startDate.getFullYear() + '-' + 
                   String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(startDate.getDate()).padStart(2, '0');
  
  const endStr = today.getFullYear() + '-' + 
                 String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                 String(today.getDate()).padStart(2, '0');
  
  return {
    start: startStr,
    end: endStr
  };
}

function getDaytimeRollingMonthDateRange(): { start: string; end: string } {
  const now = new Date();
  
  const maxMonths = parseInt(CHAT_MAX_MONTHS, 10);
  const monthsToSubtract = isNaN(maxMonths) || maxMonths <= 0 ? 1 : maxMonths;
  
  const startDate = new Date(now);
  startDate.setMonth(now.getMonth() - monthsToSubtract);

  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 1);
  
  return {
    start: startDate.toISOString(),
    end: endDate.toISOString()
  };
}

export function createChatsMessagesRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  let dateFilter: string;
  
  if (CHAT_DATETIME_SCHEDULE === 'Daytime') {
    const dateRange = getDaytimeRollingMonthDateRange();
    dateFilter = `lastModifiedDateTime gt ${dateRange.start} and lastModifiedDateTime lt ${dateRange.end}`;
    
  } else if (CHAT_DATETIME_SCHEDULE === 'Nighttime') {
    const dateRange = getNighttimeRollingMonthDateRange();
    dateFilter = `lastModifiedDateTime gt ${dateRange.start}T00:00:00Z and lastModifiedDateTime lt ${dateRange.end}T23:59:59Z`;

  } else {
    dateFilter = '';
  }

  return [{
    id: `${chatId}`,
    method: 'GET',
    url: `/chats/${chatId}/messages?$top=${CHAT_SEARCH_SIZE}&$filter=${dateFilter}`
  }];
}

export function createChatsMembersRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  return [{
    id: `${chatId}`,
    method: 'GET',
    url: `/chats/${chatId}/members`
  }];
}