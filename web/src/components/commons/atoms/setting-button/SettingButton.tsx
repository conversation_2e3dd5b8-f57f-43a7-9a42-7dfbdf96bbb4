import React from 'react';
import { Button, SettingsIcon } from '@fluentui/react-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';

// CSS
import './SettingButton.scss';

type SettingsButtonProps = {
  onClick?: (toBe: boolean, e: React.SyntheticEvent<HTMLElement>) => void;
  isActive?: boolean;
  className?: string;
};

const SettingsButton: React.FC<SettingsButtonProps> = ((props) => {
  const {
    onClick,
    isActive,
    className,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('settings-button', className);
    return mergedClassName(base, isActive ? 'is-active' : undefined);
  }, [className, isActive]);

  // クリックハンドラ
  const handleOnClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (onClick) onClick(!isActive, e);
  }, [onClick, isActive]);

  return (
    <Button
      text
      onClick={handleOnClick}
      className={rootClassName}
    >
      <SettingsIcon className="settings-icon" />
    </Button>
  );
});

SettingsButton.defaultProps = {
  onClick: undefined,
  isActive: false,
  className: '',
};

export default SettingsButton;
