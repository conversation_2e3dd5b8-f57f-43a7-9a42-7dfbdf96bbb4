import * as React from 'react';
import '@testing-library/jest-dom';
import { render, fireEvent } from '@testing-library/react';
import SplitViewListTabs from './SplitViewListTabs';
import { SearchListMode } from '../types/SearchListMode';
import { queryElem, queryElems } from '../../../../utilities/test';

describe('SplitViewListTabs', () => {
  const onClickMock = jest.fn();
  const reportEventMock = jest.fn();

  beforeEach(() => {
    onClickMock.mockClear();
    reportEventMock.mockClear();
  });

  describe('when mode = DEFAULT', () => {
    const mode = SearchListMode.DEFAULT;
    // mode=DEFAULT のときは Chat モードがアクティブではない
    const isActive = false;

    it('should have the Default tab active', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={mode}
          isActive={isActive}
          reportEvent={reportEventMock}
          isAISearchEnabled
        />,
      );
      const activeTab = queryElem(container, 'a[aria-selected="true"]');
      expect(activeTab).toHaveTextContent('Default');
    });

    it('does not call onClick when clicking the active (Default) tab', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={mode}
          isActive={isActive}
          onClick={onClickMock}
          reportEvent={reportEventMock}
          isAISearchEnabled
        />,
      );
      fireEvent.click(queryElem(container, 'a[aria-selected="true"]'));
      expect(onClickMock).not.toBeCalled();
    });

    it('calls onClick with true when the inactive (Chat) tab is clicked', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={mode}
          isActive={isActive}
          onClick={onClickMock}
          reportEvent={reportEventMock}
          isAISearchEnabled
        />,
      );
      fireEvent.click(queryElems(container, 'a[aria-selected="false"]', 0));
      expect(onClickMock).toBeCalledTimes(1);
      expect(onClickMock).toBeCalledWith(true, expect.any(Object));
    });
  });

  describe('when mode = Chat', () => {
    const mode = SearchListMode.Chat;
    // mode=Chat のときは Chat モードがアクティブ
    const isActive = true;

    it('should have the Chat tab active', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={mode}
          isActive={isActive}
          reportEvent={reportEventMock}
          isAISearchEnabled
        />,
      );
      const activeTab = queryElem(container, 'a[aria-selected="true"]');
      expect(activeTab).toHaveTextContent('AISearch');
    });

    it('calls onClick with false when the inactive (Default) tab is clicked', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={mode}
          isActive={isActive}
          onClick={onClickMock}
          reportEvent={reportEventMock}
          isAISearchEnabled
        />,
      );
      fireEvent.click(queryElems(container, 'a[aria-selected="false"]', 0));
      expect(onClickMock).toBeCalledTimes(1);
      expect(onClickMock).toBeCalledWith(false, expect.any(Object));
    });

    it('does not call onClick when clicking the active (Chat) tab', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={mode}
          isActive={isActive}
          onClick={onClickMock}
          reportEvent={reportEventMock}
          isAISearchEnabled
        />,
      );
      fireEvent.click(queryElem(container, 'a[aria-selected="true"]'));
      expect(onClickMock).not.toBeCalled();
    });
  });

  describe('isAISearchEnabledがfalseの場合', () => {
    // isAISearchEnabledプロパティがfalseの場合の動作をテスト
    it('isAISearchEnabledがfalseの場合、コンポーネントが表示されない', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={SearchListMode.DEFAULT}
          isActive={false}
          reportEvent={reportEventMock}
          isAISearchEnabled={false}
        />,
      );

      // タブコンテナが存在しないことを確認
      const tabContainer = container.querySelector('.split-view-list-tabs-container');
      expect(tabContainer).toBeNull();
    });

    it('isAISearchEnabledがfalseでChatモードが指定されても、コンポーネントが表示されない', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={SearchListMode.Chat}
          isActive
          reportEvent={reportEventMock}
          isAISearchEnabled={false}
        />,
      );

      // タブコンテナが存在しないことを確認
      const tabContainer = container.querySelector('.split-view-list-tabs-container');
      expect(tabContainer).toBeNull();
    });

    it('isAISearchEnabledがfalseの場合、onClickが呼ばれない', () => {
      const { container } = render(
        <SplitViewListTabs
          mode={SearchListMode.DEFAULT}
          isActive={false}
          onClick={onClickMock}
          reportEvent={reportEventMock}
          isAISearchEnabled={false}
        />,
      );

      // コンポーネントが表示されないため、クリックイベントも発生しない
      expect(container.firstChild).toBeNull();
      expect(onClickMock).not.toBeCalled();
    });
  });
});
