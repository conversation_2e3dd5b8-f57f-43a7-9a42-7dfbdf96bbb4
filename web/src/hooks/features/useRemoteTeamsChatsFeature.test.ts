import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import useRemoteTeamsChatsFeature, {
  addRemoteTeamsChatsImpl,
  deleteRemoteTeamsChatsImpl,
  sendQueues,
  UseRemoteTeamsChatsError,
} from './useRemoteTeamsChatsFeature';
import { ITeamsChatsItem, IRepositoryTeamsChatsQueue } from '../../types/IGeraniumAttaneDB';
import { UseTeamsChatsApiReturnType } from '../accessors/useTeamsChatsApiAccessor';
import { UseTeamsChatsRepositoryReturnType } from '../accessors/useTeamsChatsRepositoryAccessor';

// environment をモック
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

describe('useRemoteTeamsChatsFeature', () => {
  // モックデータの作成
  const mockEventReporter: EventReporter = jest.fn();

  const mockTeamsChatsItem: ITeamsChatsItem = {
    id: 'chat-123',
    name: 'Test Chat',
    type: 'チャット',
    chatType: 'oneOnOne',
    countId: 1,
  };

  const mockRepositoryReturn: UseTeamsChatsRepositoryReturnType = {
    isInitialized: true,
    isTransactionPending: false,
    allTeamsChats: [],
    retrieveTeamsChats: jest.fn(),
    addTeamsChats: jest.fn(),
    deleteTeamsChats: jest.fn(),
    replaceTeamsChats: jest.fn(),
    getTeamsChatsQueues: jest.fn(),
    addTeamsChatsQueue: jest.fn(),
    deleteTeamsChatsQueue: jest.fn(),
    replaceTeamsChatsQueues: jest.fn(),
  };

  const mockApiReturn: UseTeamsChatsApiReturnType = {
    getTeamsChatsApi: jest.fn(),
    postTeamsChatsApi: jest.fn(),
    deleteTeamsChatsApi: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useRemoteTeamsChatsFeature hook', () => {
    it('必要な関数を返すこと', () => {
      const { result } = renderHook(() => useRemoteTeamsChatsFeature(
        mockRepositoryReturn, mockApiReturn, mockEventReporter,
      ));

      expect(result.current.addRemoteTeamsChats).toBeDefined();
      expect(result.current.deleteRemoteTeamsChats).toBeDefined();
      expect(result.current.fetchRemoteTeamsChats).toBeDefined();
    });

    it('初期化時にfetchRemoteTeamsChatsが呼ばれること', async () => {
      const mockGetTeamsChatsApi = jest.fn().mockResolvedValue([]);
      const mockGetTeamsChatsQueues = jest.fn().mockResolvedValue([]);
      const mockReplaceTeamsChats = jest.fn().mockResolvedValue(undefined);

      const repositoryWithMocks = {
        ...mockRepositoryReturn,
        getTeamsChatsQueues: mockGetTeamsChatsQueues,
        replaceTeamsChats: mockReplaceTeamsChats,
      };

      const apiWithMocks = {
        ...mockApiReturn,
        getTeamsChatsApi: mockGetTeamsChatsApi,
      };

      renderHook(() => useRemoteTeamsChatsFeature(
        repositoryWithMocks, apiWithMocks, mockEventReporter,
      ));

      // useEffectが実行されるまで待機
      await new Promise((resolve) => {
        setTimeout(resolve, 100);
      });
      expect(mockGetTeamsChatsApi).toHaveBeenCalled();
    });
  });

  describe('addRemoteTeamsChatsImpl', () => {
    it('必要な依存関数が存在しない場合、何もしないこと', async () => {
      const result = await addRemoteTeamsChatsImpl(
        undefined, // addTeamsChatsQueue
        undefined, // getTeamsChatsQueues
        mockRepositoryReturn.deleteTeamsChatsQueue,
        mockApiReturn.deleteTeamsChatsApi,
        mockApiReturn.postTeamsChatsApi,
        mockTeamsChatsItem,
        mockEventReporter,
      );

      expect(result).toBeUndefined();
    });

    it('正常にキューに追加し、送信処理を実行すること', async () => {
      const mockAddTeamsChatsQueue = jest.fn().mockResolvedValue(undefined);
      const mockGetTeamsChatsQueues = jest.fn().mockResolvedValue([]);

      await addRemoteTeamsChatsImpl(
        mockAddTeamsChatsQueue,
        mockGetTeamsChatsQueues,
        mockRepositoryReturn.deleteTeamsChatsQueue,
        mockApiReturn.deleteTeamsChatsApi,
        mockApiReturn.postTeamsChatsApi,
        mockTeamsChatsItem,
        mockEventReporter,
      );

      expect(mockAddTeamsChatsQueue).toHaveBeenCalledWith(mockTeamsChatsItem, 'PUT');
      expect(mockGetTeamsChatsQueues).toHaveBeenCalled();
    });
  });

  describe('deleteRemoteTeamsChatsImpl', () => {
    it('必要な依存関数が存在しない場合、何もしないこと', async () => {
      const result = await deleteRemoteTeamsChatsImpl(
        undefined, // addTeamsChatsQueue
        undefined, // getTeamsChatsQueues
        mockRepositoryReturn.deleteTeamsChatsQueue,
        mockApiReturn.deleteTeamsChatsApi,
        mockApiReturn.postTeamsChatsApi,
        mockTeamsChatsItem,
        mockEventReporter,
      );

      expect(result).toBeUndefined();
    });

    it('正常に削除キューに追加し、送信処理を実行すること', async () => {
      const mockAddTeamsChatsQueue = jest.fn().mockResolvedValue(undefined);
      const mockGetTeamsChatsQueues = jest.fn().mockResolvedValue([]);

      await deleteRemoteTeamsChatsImpl(
        mockAddTeamsChatsQueue,
        mockGetTeamsChatsQueues,
        mockRepositoryReturn.deleteTeamsChatsQueue,
        mockApiReturn.deleteTeamsChatsApi,
        mockApiReturn.postTeamsChatsApi,
        mockTeamsChatsItem,
        mockEventReporter,
      );

      expect(mockAddTeamsChatsQueue).toHaveBeenCalledWith(mockTeamsChatsItem, 'DELETE');
      expect(mockGetTeamsChatsQueues).toHaveBeenCalled();
    });
  });

  describe('sendQueues', () => {
    const mockQueue: IRepositoryTeamsChatsQueue = {
      date: new Date(),
      type: 'PUT',
      data: mockTeamsChatsItem,
    };

    it('キューが空の場合、何もしないこと', async () => {
      const mockPostTeamsChatsApi = jest.fn();
      const mockDeleteTeamsChatsApi = jest.fn();
      const mockDeleteTeamsChatsQueue = jest.fn();

      await sendQueues(
        [],
        mockPostTeamsChatsApi,
        mockDeleteTeamsChatsApi,
        mockDeleteTeamsChatsQueue,
        mockEventReporter,
      );

      expect(mockPostTeamsChatsApi).not.toHaveBeenCalled();
      expect(mockDeleteTeamsChatsApi).not.toHaveBeenCalled();
    });

    it('PUTキューを正常に処理すること', async () => {
      const mockPostTeamsChatsApi = jest.fn().mockResolvedValue(undefined);
      const mockDeleteTeamsChatsApi = jest.fn();
      const mockDeleteTeamsChatsQueue = jest.fn().mockResolvedValue(undefined);

      await sendQueues(
        [mockQueue],
        mockPostTeamsChatsApi,
        mockDeleteTeamsChatsApi,
        mockDeleteTeamsChatsQueue,
        mockEventReporter,
      );

      expect(mockPostTeamsChatsApi).toHaveBeenCalledWith({
        countId: mockTeamsChatsItem.countId,
        chatType: mockTeamsChatsItem.chatType,
        name: mockTeamsChatsItem.name,
        chatId: mockTeamsChatsItem.id,
      });
      expect(mockDeleteTeamsChatsQueue).toHaveBeenCalled();
    });

    it('DELETEキューを正常に処理すること', async () => {
      const deleteQueue: IRepositoryTeamsChatsQueue = {
        ...mockQueue,
        type: 'DELETE',
      };

      const mockPostTeamsChatsApi = jest.fn();
      const mockDeleteTeamsChatsApi = jest.fn().mockResolvedValue(undefined);
      const mockDeleteTeamsChatsQueue = jest.fn().mockResolvedValue(undefined);

      await sendQueues(
        [deleteQueue],
        mockPostTeamsChatsApi,
        mockDeleteTeamsChatsApi,
        mockDeleteTeamsChatsQueue,
        mockEventReporter,
      );

      expect(mockDeleteTeamsChatsApi).toHaveBeenCalledWith(mockTeamsChatsItem.id);
      expect(mockDeleteTeamsChatsQueue).toHaveBeenCalled();
    });

    it('API呼び出しでエラーが発生した場合、エラーレポートを送信し処理を停止すること', async () => {
      const mockError = new Error('API Error');
      const mockPostTeamsChatsApi = jest.fn().mockRejectedValue(mockError);
      const mockDeleteTeamsChatsApi = jest.fn();
      const mockDeleteTeamsChatsQueue = jest.fn();

      await sendQueues(
        [mockQueue],
        mockPostTeamsChatsApi,
        mockDeleteTeamsChatsApi,
        mockDeleteTeamsChatsQueue,
        mockEventReporter,
      );

      expect(mockEventReporter).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: UseRemoteTeamsChatsError.QUEUE_SEND_FAIL,
        error: mockError,
      });
    });
  });
});
