import * as React from 'react';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import useTeamsChatsApiAccessor, {
  DeleteTeamsChatsApi,
  PostTeamsChatsApi,
  ITeamsChatsRequest,
  ITeamsChatsResponse,
} from '../accessors/useTeamsChatsApiAccessor';
import useTeamsChatsRepositoryAccessor, {
  AddTeamsChatsQueue,
  DeleteTeamsChatsQueue,
  GetTeamsChatsQueues,
  ReplaceTeamsChatsQueues,
} from '../accessors/useTeamsChatsRepositoryAccessor';
import { IRepositoryTeamsChatsQueue, ITeamsChatsItem } from '../../types/IGeraniumAttaneDB';

export type AddRemoteTeamsChats = (data: ITeamsChatsItem) => Promise<void>;
export type DeleteRemoteTeamsChats = (data: ITeamsChatsItem) => Promise<void>;
export type FetchRemoteTeamsChats = () => Promise<void>;

type UseRemoteTeamsChatsFeatureReturnType = {
  addRemoteTeamsChats: AddRemoteTeamsChats | undefined,
  deleteRemoteTeamsChats: DeleteRemoteTeamsChats | undefined,
  fetchRemoteTeamsChats: FetchRemoteTeamsChats | undefined,
};

export const UseRemoteTeamsChatsError = {
  IS_FIRST_TIME_SYNC: 'IS_FIRST_TIME_SYNC',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  QUEUE_SEND_FAIL: 'QUEUE_SEND_FAIL',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

/**
 * TeamsChatsアイテムをAPIリクエスト形式に変換
 */
function convertToApiRequest(item: ITeamsChatsItem): ITeamsChatsRequest {
  return {
    countId: item.countId,
    chatType: item.chatType,
    name: item.name,
    ...(item.type === 'チャット' && {
      chatId: item.id,
    }),
    ...(item.type === 'チャネル' && {
      teamId: item.teamId,
      channelId: item.id,
    }),
  };
}

/**
 * APIレスポンスをローカルTeamsChatsアイテム形式に変換
 */
function convertFromApiResponse(response: ITeamsChatsResponse): ITeamsChatsItem {
  const isChannel = response.chatType === 'TeamsChannel';
  return {
    id: isChannel ? (response.channelId || '') : (response.chatId || ''),
    name: response.name,
    type: isChannel ? 'チャネル' : 'チャット',
    chatType: response.chatType as 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel',
    teamId: response.teamId,
    countId: response.countId,
  };
}

/**
 * キューの変更をTeamsChatsに適用する
 */
function applyQueueToTeamsChats(
  teamsChats: ITeamsChatsItem[],
  queues: IRepositoryTeamsChatsQueue[],
): ITeamsChatsItem[] {
  let result = [...teamsChats];

  queues.forEach((queue) => {
    if (queue.type === 'PUT') {
      // 既存アイテムを更新または新規追加
      const existingIndex = result.findIndex((item) => item.id === queue.data.id);
      if (existingIndex >= 0) {
        result[existingIndex] = queue.data;
      } else {
        result.push(queue.data);
      }
    } else if (queue.type === 'DELETE') {
      // アイテムを削除
      result = result.filter((item) => item.id !== queue.data.id);
    }
  });

  return result;
}
/**
 * ローカルとリモートのTeamsChatsをマージする
 * countIdが重複する場合はリモートを優先
 */
function mergeLocalRemoteTeamsChats(
  localTeamsChats: ITeamsChatsItem[],
  remoteTeamsChats: ITeamsChatsItem[],
  localQueues: IRepositoryTeamsChatsQueue[],
): ITeamsChatsItem[] {
  // リモートデータをベースとする
  const mergedItems = [...remoteTeamsChats];

  // ローカルにのみ存在するアイテムを追加
  localTeamsChats.forEach((localItem) => {
    const existsInRemote = remoteTeamsChats.some((remoteItem) => remoteItem.id === localItem.id);
    if (!existsInRemote) {
      mergedItems.push(localItem);
    }
  });

  // キューの変更を適用
  if (localQueues && localQueues.length > 0) {
    return applyQueueToTeamsChats(mergedItems, localQueues);
  }

  return mergedItems;
}

/**
 * 1件づつQueueを送信し、成功したらqueueから消す
 * 失敗したらそれ以上は送信しない
 */
export function sendQueue(
  q: IRepositoryTeamsChatsQueue,
  postTeamsChatsApi: PostTeamsChatsApi,
  deleteTeamsChatsApi: DeleteTeamsChatsApi,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue,
): Promise<void> {

  // PUTの場合
  if (q.type === 'PUT') {
    const request = convertToApiRequest(q.data);
    return postTeamsChatsApi(request)
      .then(() => deleteTeamsChatsQueue(q.data.id));
  }

  // DELETEの場合
  return deleteTeamsChatsApi(q.data.id)
    .then(() => deleteTeamsChatsQueue(q.data.id));
}

/**
 * キューを直列で逐次送信し、成功したキューは削除する
 * 途中で失敗した場合はキューを保持し、送信を止める
 */
export async function sendQueues(
  queues: IRepositoryTeamsChatsQueue[] | null,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined,
  eventReporter: EventReporter,
): Promise<void> {

  if (!queues || !postTeamsChatsApi || !deleteTeamsChatsApi || !deleteTeamsChatsQueue) {
    return Promise.resolve();
  }

  // 0件時はwhileを回さずに終了
  if (queues.length === 0) {
    return Promise.resolve();
  }

  // whileループを開始
  let isContinue = true;
  let counter = 0;

  while (isContinue) {
    const q = queues[counter];
    if (!q) {
      isContinue = false;
      break;
    }

    // 直列処理したいのでPromise.allにしない
    // キューの中身を逐次APIに投げ、失敗したらそこで止める
    // eslint-disable-next-line no-await-in-loop
    const result = await sendQueue(q, postTeamsChatsApi, deleteTeamsChatsApi, deleteTeamsChatsQueue)
      // Error型でcatchできるはずだが、whileを抜けられるように必ずError型を返させる
      .catch((reason: Error | unknown) => (reason instanceof Error
        ? reason
        : new Error(UseRemoteTeamsChatsError.UNKNOWN_ERROR)));

    // 失敗した場合はwhileを抜ける
    if (result instanceof Error) {
      eventReporter({
        type: EventReportType.SYS_ERROR,
        name: UseRemoteTeamsChatsError.QUEUE_SEND_FAIL,
        error: result,
      });
      isContinue = false;
      break;
    }

    counter += 1;
  }

  return Promise.resolve();
}

/**
 * ローカルとリモートの両方にTeamsChatsを追加する機能の実装
 */
export async function addRemoteTeamsChatsImpl(
  addTeamsChatsQueue: AddTeamsChatsQueue | undefined,
  getTeamsChatsQueues: GetTeamsChatsQueues | undefined,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  item: ITeamsChatsItem,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getTeamsChatsQueues
    || !addTeamsChatsQueue) {
    return Promise.resolve();
  }

  // キューとTeamsChatsの両方に追加
  await addTeamsChatsQueue(item, 'PUT');

  // キュー送信を試行
  return sendQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * ローカルとリモートの両方からTeamsChatsを削除する機能の実装
 */
export async function deleteRemoteTeamsChatsImpl(
  addTeamsChatsQueue: AddTeamsChatsQueue | undefined,
  getTeamsChatsQueues: GetTeamsChatsQueues | undefined,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  item: ITeamsChatsItem,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getTeamsChatsQueues
    || !addTeamsChatsQueue
  ) {
    return Promise.resolve();
  }

  // 削除キューの追加とTeamsChats削除
  await addTeamsChatsQueue(item, 'DELETE');

  // キュー送信を試行
  return sendQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * queueをTeamsChatsのPUTで全件置き換える
 */
export function replaceQueuesByTeamsChats(
  teamsChats: ITeamsChatsItem[],
  replaceTeamsChatsQueues: ReplaceTeamsChatsQueues,
): Promise<void> {

  const queues: IRepositoryTeamsChatsQueue[] = teamsChats.map((item) => ({
    type: 'PUT',
    data: item,
    date: new Date(),
  }));

  return replaceTeamsChatsQueues(queues);
}

/**
 * リモート上のAPIとブラウザローカルのTeamsChatsデータの追加/削除/同期処理をする機能
 */
const useRemoteTeamsChatsFeature = (
  useRepositoryReturn: ReturnType<typeof useTeamsChatsRepositoryAccessor>,
  useTeamsChatsApiReturn: ReturnType<typeof useTeamsChatsApiAccessor>,
  eventReporter: EventReporter,
): UseRemoteTeamsChatsFeatureReturnType => {

  // useRepositoryからメンバ取得
  const {
    replaceTeamsChats,
    getTeamsChatsQueues,
    addTeamsChatsQueue,
    deleteTeamsChatsQueue,
    replaceTeamsChatsQueues,
    allTeamsChats,
  } = useRepositoryReturn;

  // useApiからメンバ取得
  const {
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
  } = useTeamsChatsApiReturn;

  const [isInitialized, setIsInitialized] = React.useState<'INITIAL' | 'PENDING' | 'DONE'>('INITIAL');
  const initialRunTrigger = React.useRef(false);

  // オンライン復帰時のキュー送信
  React.useEffect(() => {
    const handleOnline = () => {
      if (isInitialized === 'DONE' && getTeamsChatsQueues && postTeamsChatsApi && deleteTeamsChatsApi && deleteTeamsChatsQueue) {
        // オンライン復帰時にキューを送信
        getTeamsChatsQueues().then((queues) => {
          if (queues && queues.length > 0) {
            sendQueues(queues,
              postTeamsChatsApi,
              deleteTeamsChatsApi,
              deleteTeamsChatsQueue,
              eventReporter);
          }
        });
      }
    };

    window.addEventListener('online', handleOnline);
    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, [isInitialized,
    getTeamsChatsQueues,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter]);

  /**
   * リモート取得処理の実装
   */
  const fetchRemoteTeamsChats = React.useCallback(async (onSuccess?: () => void) => {
    if (isInitialized !== 'PENDING') {
      setIsInitialized('PENDING');

      try {
        // リモートからTeamsChatsデータを取得
        if (getTeamsChatsApi && replaceTeamsChats && getTeamsChatsQueues) {
          try {
            const [remoteTeamsChatsResponse, localQueues] = await Promise.all([
              getTeamsChatsApi(),
              getTeamsChatsQueues(),
            ]);

            // APIレスポンスをローカル形式に変換
            const remoteTeamsChats = remoteTeamsChatsResponse.map(convertFromApiResponse);

            // 現在のローカルデータを取得（replaceTeamsChatsの前に）
            const currentLocalTeamsChats = allTeamsChats;

            // ローカルとリモートをマージ
            const mergedTeamsChats = mergeLocalRemoteTeamsChats(
              currentLocalTeamsChats,
              remoteTeamsChats,
              localQueues,
            );

            // マージ結果をローカルIndexedDBに保存
            await replaceTeamsChats(mergedTeamsChats);
          } catch (fetchError) {
            // リモート取得エラーは無視して続行（オフライン対応）
            eventReporter({
              type: EventReportType.SYS_ERROR,
              name: UseRemoteTeamsChatsError.IS_OFFLINE_OR_SOMETHING_WRONG,
              error: fetchError as Error,
            });
          }
        }

        // キュー送信を試行
        await sendQueues(
          await getTeamsChatsQueues(),
          postTeamsChatsApi,
          deleteTeamsChatsApi,
          deleteTeamsChatsQueue,
          eventReporter,
        );
      } finally {
        setIsInitialized('DONE');
      }

      if (onSuccess) {
        onSuccess();
      }
    }
  }, [isInitialized,
    getTeamsChatsApi,
    replaceTeamsChats,
    getTeamsChatsQueues,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
    allTeamsChats]);

  /**
   * ローカルとリモートの両方にTeamsChats追加
   */
  const addRemoteTeamsChats: AddRemoteTeamsChats = React.useCallback(
    async (item: ITeamsChatsItem) => addRemoteTeamsChatsImpl(
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      deleteTeamsChatsQueue,
      deleteTeamsChatsApi,
      postTeamsChatsApi,
      item,
      eventReporter,
    ),
    [
      addTeamsChatsQueue,
      deleteTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      eventReporter,
    ],
  );

  /**
   * ローカルとリモートの両方からTeamsChats削除
   */
  const deleteRemoteTeamsChats: DeleteRemoteTeamsChats = React.useCallback(
    async (item: ITeamsChatsItem) => deleteRemoteTeamsChatsImpl(
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      deleteTeamsChatsQueue,
      deleteTeamsChatsApi,
      postTeamsChatsApi,
      item,
      eventReporter,
    ),
    [
      addTeamsChatsQueue,
      deleteTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      eventReporter,
    ],
  );

  // 初回同期処理の実行
  React.useEffect(() => {
    if (
      !getTeamsChatsApi
      || !getTeamsChatsQueues
      || !replaceTeamsChats
      || !addTeamsChatsQueue
      || !replaceTeamsChatsQueues
    ) return;

    if (initialRunTrigger.current) return;

    initialRunTrigger.current = true;
    fetchRemoteTeamsChats();
  }, [
    fetchRemoteTeamsChats,
    getTeamsChatsApi,
    addTeamsChatsQueue,
    getTeamsChatsQueues,
    replaceTeamsChatsQueues,
    replaceTeamsChats,
  ]);

  return {
    addRemoteTeamsChats,
    deleteRemoteTeamsChats,
    fetchRemoteTeamsChats,
  };
};

export default useRemoteTeamsChatsFeature;
