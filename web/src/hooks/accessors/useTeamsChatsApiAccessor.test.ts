import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react-hooks';
import useTeamsChatsApiAccessor, {
  createPostTeamsChatsUrl,
  createGetTeamsChatsUrl,
  UseTeamsChatsApiAccessorError,
} from './useTeamsChatsApiAccessor';
import { Reporters } from '../../types/Reporters';

// environment をモック
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

// fetchUrlRes と getUniqueNameByToken をモック
const fetchUrlResMock = jest.fn();
const getUniqueNameByTokenMock = jest.fn();
jest.mock('../../utilities/commonFunction', () => ({
  __esModule: true,
  fetchUrlRes: (...args: unknown[]) => (fetchUrlResMock as any)(...args),
}));
jest.mock('../../utilities/token/jwt', () => ({
  __esModule: true,
  getUniqueNameByToken: (...args: unknown[]) => (getUniqueNameByTokenMock as any)(...args),
}));

// ダミー reporter
const reportMock = jest.fn();
const reporters: Reporters = [reportMock as any, jest.fn() as any];

const tokenProvider = jest.fn().mockResolvedValue('token');

describe('useTeamsChatsApiAccessor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getUniqueNameByTokenMock.mockResolvedValue(['token', 'user-1']);
  });

  describe('URL ヘルパー', () => {
    it('createPostTeamsChatsUrl: ユーザーIDからURLを構築できる', () => {
      expect(createPostTeamsChatsUrl('u')).toBe('https://example.com/api/users/u/teams-chats');
    });
    it('createGetTeamsChatsUrl: ユーザーIDからURLを構築できる', () => {
      expect(createGetTeamsChatsUrl('u')).toBe('https://example.com/api/users/u/teams-chats');
    });
  });

  describe('トークン未指定の時', () => {
    it('各API関数は undefined を返す', () => {
      const { result } = renderHook(() => useTeamsChatsApiAccessor(undefined, reporters));
      expect(result.current.postTeamsChatsApi).toBeUndefined();
      expect(result.current.deleteTeamsChatsApi).toBeUndefined();
      expect(result.current.getTeamsChatsApi).toBeUndefined();
    });
  });

  describe('postTeamsChatsApi', () => {
    it('201/200 なら resolve する', async () => {
      fetchUrlResMock.mockResolvedValue({ status: 201, statusText: 'Created' });
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));

      await act(async () => {
        await expect(result.current.postTeamsChatsApi?.({
          countId: 1,
          chatType: 'oneOnOne',
          chatId: 'c1',
          name: 'テストチャット',
        })).resolves.toBeUndefined();
      });

      expect(fetchUrlResMock).toHaveBeenCalled();
      expect(reportMock).not.toHaveBeenCalled();
    });

    it('パラメータ不足ならエラー', async () => {
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await expect(result.current.postTeamsChatsApi?.({
        // countId なし
        // chatId なし
        chatType: 'oneOnOne',
      } as any)).rejects.toThrow(UseTeamsChatsApiAccessorError.MISSING_PARAMS);
    });

    it('countId 範囲外ならエラー', async () => {
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await expect(result.current.postTeamsChatsApi?.({
        countId: 0,
        chatType: 'oneOnOne',
        chatId: 'c1',
      } as any)).rejects.toThrow(UseTeamsChatsApiAccessorError.MISSING_PARAMS);
    });

    it('API エラー時は reporter が呼ばれ throw する', async () => {
      fetchUrlResMock.mockResolvedValue({ status: 500, statusText: 'NG' });
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));

      await expect(result.current.postTeamsChatsApi?.({
        countId: 1,
        chatType: 'oneOnOne',
        chatId: 'c1',
        name: 'テストチャット',
      })).rejects.toThrow('API Error: 500 NG');

      expect(reportMock).toHaveBeenCalled();
    });
  });

  describe('deleteTeamsChatsApi', () => {
    it('200/204 なら resolve する', async () => {
      fetchUrlResMock.mockResolvedValue({ status: 204, statusText: 'No Content' });
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await act(async () => {
        await expect(result.current.deleteTeamsChatsApi?.('chat-1')).resolves.toBeUndefined();
      });
      expect(fetchUrlResMock).toHaveBeenCalled();
    });

    it('chatId 無しはエラー', async () => {
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await expect(result.current.deleteTeamsChatsApi?.('')).rejects.toThrow(UseTeamsChatsApiAccessorError.MISSING_PARAMS);
    });

    it('API エラー時は reporter が呼ばれ throw する', async () => {
      fetchUrlResMock.mockResolvedValue({ status: 400, statusText: 'Bad' });
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await expect(result.current.deleteTeamsChatsApi?.('chat-1')).rejects.toThrow('API Error: 400 Bad');
      expect(reportMock).toHaveBeenCalled();
    });
  });

  describe('getTeamsChatsApi', () => {
    it('200 なら json を返す', async () => {
      fetchUrlResMock.mockResolvedValue({ status: 200, statusText: 'OK', json: async () => ([{ Id: '1' }]) });
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await expect(result.current.getTeamsChatsApi?.()).resolves.toStrictEqual([{ Id: '1' }]);
    });

    it('API エラー時は reporter が呼ばれ throw する', async () => {
      fetchUrlResMock.mockResolvedValue({ status: 500, statusText: 'ERR' });
      const { result } = renderHook(() => useTeamsChatsApiAccessor(tokenProvider, reporters));
      await expect(result.current.getTeamsChatsApi?.()).rejects.toThrow('API Error: 500 ERR');
      expect(reportMock).toHaveBeenCalled();
    });
  });
});
