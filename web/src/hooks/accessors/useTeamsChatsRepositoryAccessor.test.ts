import { renderHook, act } from '@testing-library/react-hooks';
import { openDB } from 'idb';
import {
  clearIdbMocks, dbMock, storeMock,
} from '../../mocks/idb';
import useTeamsChatsRepositoryAccessor, {
  UseTeamsChatsRepositoryError,
} from './useTeamsChatsRepositoryAccessor';
import useIndexedDbAccessor from './useIndexedDbAccessor';
import { ITeamsChatsItem, IRepositoryTeamsChatsQueue } from '../../types/IGeraniumAttaneDB';

jest.mock('idb', () => ({
  openDB: jest.fn(),
}));
jest.mock('../../utilities/environment');

// openDBのmock
const openDBMock = (openDB as jest.Mock).mockResolvedValue(dbMock);

// テスト用のTeamsChatsアイテムを作成するヘルパー関数
function createTeamsChatsItem(override: Partial<ITeamsChatsItem> = {}): ITeamsChatsItem {
  return {
    id: 'chat-001',
    name: 'テストチャット',
    type: 'チャット',
    chatType: 'oneOnOne',
    countId: 1,
    ...override,
  };
}

// テスト用のTeamsChatsキューを作成するヘルパー関数
function createTeamsChatsQueue(override: {
  type?: 'PUT' | 'DELETE',
  data?: Partial<ITeamsChatsItem>,
  date?: Date,
} = {}): IRepositoryTeamsChatsQueue {
  return {
    type: override.type || 'PUT',
    data: createTeamsChatsItem(override?.data ?? {}),
    date: override.date || new Date('2023-04-01T00:00:00.000Z'),
  };
}

describe('useTeamsChatsRepositoryAccessor', () => {
  async function getHook(initialEntries: ITeamsChatsItem[] = []) {
    dbMock.getAll.mockResolvedValue(initialEntries);
    const getHookResult = renderHook(
      () => useTeamsChatsRepositoryAccessor(useIndexedDbAccessor()[0]),
    );
    const { result, waitForNextUpdate } = getHookResult;

    // 初期化前は状態がfalse
    expect(result.current.isInitialized).toBe(false);
    expect(result.current.isTransactionPending).toBe(false);
    expect(result.current.allTeamsChats).toEqual([]);

    await waitForNextUpdate();

    // 初期化後は状態がtrue
    expect(result.current.isInitialized).toBe(true);
    expect(result.current.isTransactionPending).toBe(false);

    // openDBが実行されている（初期化時）
    expect(openDBMock).toHaveBeenCalled();

    openDBMock.mockClear();
    clearIdbMocks();

    return getHookResult;
  }

  beforeEach(() => {
    openDBMock.mockClear();
    clearIdbMocks();
  });

  describe('初期化処理', () => {
    it('openDBが未指定の場合、初期化されない', () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      expect(result.current.isInitialized).toBe(false);
      expect(result.current.allTeamsChats).toEqual([]);
    });

    it('初期データが正しく読み込まれる', async () => {
      const initialData = [
        createTeamsChatsItem({ id: 'chat-001', countId: 1 }),
        createTeamsChatsItem({ id: 'chat-002', countId: 2 }),
      ];
      const { result } = await getHook(initialData);
      expect(result.current.allTeamsChats).toEqual(initialData);
    });
  });

  describe('retrieveTeamsChats', () => {
    it('openDBが未指定の場合、空配列を返す', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      const data = await result.current.retrieveTeamsChats();
      expect(data).toEqual([]);
    });

    it('データベースからデータを取得し、countIdでソートして返す', async () => {
      const testData = [
        createTeamsChatsItem({ id: 'chat-002', countId: 2 }),
        createTeamsChatsItem({ id: 'chat-001', countId: 1 }),
      ];

      const { result } = await getHook([]);

      // 新しいmockを設定してからretrieveTeamsChatsを呼び出す
      dbMock.getAll.mockResolvedValue(testData);
      const data = await result.current.retrieveTeamsChats();

      expect(dbMock.getAll).toHaveBeenCalledWith('teams_chats');
      expect(data).toEqual([
        createTeamsChatsItem({ id: 'chat-001', countId: 1 }),
        createTeamsChatsItem({ id: 'chat-002', countId: 2 }),
      ]);
    });
  });

  describe('addTeamsChats', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      const item = createTeamsChatsItem();
      await expect(result.current.addTeamsChats(item)).rejects.toThrow('no opendb');
    });

    it('最大件数を超える場合、MAX_TEAMS_CHATSエラーをrejectする', async () => {
      // 10件のデータで初期化
      const initialData = Array.from({ length: 10 }, (_, i) => createTeamsChatsItem({ id: `chat-${i}`, countId: i + 1 }));
      const { result } = await getHook(initialData);

      const newItem = createTeamsChatsItem({ id: 'chat-new', countId: 11 });
      await expect(result.current.addTeamsChats(newItem))
        .rejects.toBe(UseTeamsChatsRepositoryError.MAX_TEAMS_CHATS);
    });

    it('正常にアイテムを追加する', async () => {
      dbMock.put.mockResolvedValue('chat-001');
      const { result } = await getHook([]);

      const item = createTeamsChatsItem();
      await act(async () => {
        const resultId = await result.current.addTeamsChats(item);
        expect(resultId).toBe('chat-001');
      });

      expect(dbMock.put).toHaveBeenCalledWith('teams_chats', item, item.id);
      expect(dbMock.close).toHaveBeenCalled();
    });
  });

  describe('deleteTeamsChats', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      await expect(result.current.deleteTeamsChats('chat-001')).rejects.toThrow('no opendb');
    });

    it('正常にアイテムを削除する', async () => {
      dbMock.delete.mockResolvedValue(undefined);
      const { result } = await getHook([]);

      await act(async () => {
        await result.current.deleteTeamsChats('chat-001');
      });

      expect(dbMock.delete).toHaveBeenCalledWith('teams_chats', 'chat-001');
      expect(dbMock.close).toHaveBeenCalled();
    });
  });

  describe('replaceTeamsChats', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      const items = [createTeamsChatsItem()];
      await expect(result.current.replaceTeamsChats(items)).rejects.toThrow('no opendb');
    });

    it('全件置き換えを実行する', async () => {
      const { result } = await getHook([]);
      const items = [
        createTeamsChatsItem({ id: 'chat-001' }),
        createTeamsChatsItem({ id: 'chat-002' }),
      ];

      await act(async () => {
        await result.current.replaceTeamsChats(items);
      });

      expect(dbMock.transaction).toHaveBeenCalledWith('teams_chats', 'readwrite');
      expect(storeMock.clear).toHaveBeenCalled();
      expect(storeMock.put).toHaveBeenCalledTimes(2);
      expect(dbMock.close).toHaveBeenCalled();
    });

    it('空配列で全消去を実行する', async () => {
      const { result } = await getHook([]);

      await act(async () => {
        await result.current.replaceTeamsChats([]);
      });

      expect(storeMock.clear).toHaveBeenCalled();
      expect(storeMock.put).not.toHaveBeenCalled();
    });
  });

  describe('getTeamsChatsQueues', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      await expect(result.current.getTeamsChatsQueues()).rejects.toThrow('no opendb');
    });

    it('キューデータを全件取得する', async () => {
      const queueData = [createTeamsChatsQueue()];
      const { result } = await getHook([]);

      // 新しいmockを設定してからgetTeamsChatsQueuesを呼び出す
      dbMock.getAll.mockResolvedValue(queueData);
      const queues = await result.current.getTeamsChatsQueues();

      expect(dbMock.getAll).toHaveBeenCalledWith('teams_chats_queue');
      expect(queues).toEqual(queueData);
      expect(dbMock.close).toHaveBeenCalled();
    });
  });

  describe('addTeamsChatsQueue', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      const item = createTeamsChatsItem();
      await expect(result.current.addTeamsChatsQueue(item, 'PUT')).rejects.toThrow('no opendb');
    });

    it('トランザクション処理中の場合、何もしない', async () => {
      const { result } = await getHook([]);

      // トランザクション処理中の状態をシミュレート
      // isTransactionPendingをtrueにするため、まずaddTeamsChatsを開始
      const addPromise = result.current.addTeamsChats(createTeamsChatsItem());

      const item = createTeamsChatsItem({ id: 'chat-002' });

      // トランザクション処理中にaddTeamsChatsQueueを呼び出す
      await act(async () => {
        await result.current.addTeamsChatsQueue(item, 'PUT');
        // 最初のトランザクションを完了
        await addPromise;
      });

      // addTeamsChatsQueueは何もしないため、追加のトランザクションは発生しない
      expect(result.current.isTransactionPending).toBe(false);
    });

    it('PUT操作で最大件数を超える場合、MAX_TEAMS_CHATSエラーをrejectする', async () => {
      // 10件のデータで初期化
      const initialData = Array.from({ length: 10 }, (_, i) => createTeamsChatsItem({ id: `chat-${i}`, countId: i + 1 }));
      const { result } = await getHook(initialData);

      const newItem = createTeamsChatsItem({ id: 'chat-new', countId: 11 });
      await expect(result.current.addTeamsChatsQueue(newItem, 'PUT')).rejects.toBe(UseTeamsChatsRepositoryError.MAX_TEAMS_CHATS);
    });

    it('PUT操作で正常にキューとアイテムを追加する', async () => {
      storeMock.get.mockResolvedValue(undefined); // 既存キューなし
      const { result } = await getHook([]);

      const item = createTeamsChatsItem();
      await act(async () => {
        await result.current.addTeamsChatsQueue(item, 'PUT');
      });

      expect(dbMock.transaction).toHaveBeenCalledWith(['teams_chats', 'teams_chats_queue'], 'readwrite');
      expect(storeMock.put).toHaveBeenCalledTimes(2); // キューとアイテムの両方
      expect(dbMock.close).toHaveBeenCalled();
    });

    it('DELETE操作で正常にキューを追加しアイテムを削除する', async () => {
      storeMock.get.mockResolvedValue(undefined); // 既存キューなし
      const { result } = await getHook([]);

      const item = createTeamsChatsItem();
      await act(async () => {
        await result.current.addTeamsChatsQueue(item, 'DELETE');
      });

      expect(dbMock.transaction).toHaveBeenCalledWith(['teams_chats', 'teams_chats_queue'], 'readwrite');
      expect(storeMock.put).toHaveBeenCalledTimes(1); // キューのみ
      expect(storeMock.delete).toHaveBeenCalledWith(item.id); // アイテム削除
      expect(dbMock.close).toHaveBeenCalled();
    });

    it('相反するキューが存在する場合、既存キューを削除する', async () => {
      const existingQueue = createTeamsChatsQueue({ type: 'PUT' });
      storeMock.get.mockResolvedValue(existingQueue);
      const { result } = await getHook([]);

      const item = createTeamsChatsItem();
      await act(async () => {
        await result.current.addTeamsChatsQueue(item, 'DELETE');
      });

      expect(storeMock.delete).toHaveBeenCalledWith(item.id); // 既存キューを削除
      expect(dbMock.close).toHaveBeenCalled();
    });
  });

  describe('deleteTeamsChatsQueue', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      await expect(result.current.deleteTeamsChatsQueue('chat-001')).rejects.toThrow('no opendb');
    });

    it('正常にキューを削除する', async () => {
      dbMock.delete.mockResolvedValue(undefined);
      const { result } = await getHook([]);

      await result.current.deleteTeamsChatsQueue('chat-001');

      expect(dbMock.delete).toHaveBeenCalledWith('teams_chats_queue', 'chat-001');
      expect(dbMock.close).toHaveBeenCalled();
    });
  });

  describe('replaceTeamsChatsQueues', () => {
    it('openDBが未指定の場合、エラーをrejectする', async () => {
      const { result } = renderHook(() => useTeamsChatsRepositoryAccessor(undefined));
      const queues = [createTeamsChatsQueue()];
      await expect(result.current.replaceTeamsChatsQueues(queues)).rejects.toThrow('no opendb');
    });

    it('空配列の場合、何もしない', async () => {
      // 新しいhookインスタンスを作成してtransactionの呼び出し回数をリセット
      dbMock.getAll.mockResolvedValue([]);
      const { result, waitForNextUpdate } = renderHook(
        () => useTeamsChatsRepositoryAccessor(useIndexedDbAccessor()[0]),
      );
      await waitForNextUpdate();

      // mockをクリアしてからテスト実行
      dbMock.transaction.mockClear();

      await result.current.replaceTeamsChatsQueues([]);

      expect(dbMock.transaction).not.toHaveBeenCalled();
    });

    it('キューを全件置き換える', async () => {
      const { result } = await getHook([]);
      const queues = [
        createTeamsChatsQueue({ data: { id: 'chat-001' } }),
        createTeamsChatsQueue({ data: { id: 'chat-002' } }),
      ];

      await act(async () => {
        await result.current.replaceTeamsChatsQueues(queues);
      });

      expect(dbMock.transaction).toHaveBeenCalledWith('teams_chats_queue', 'readwrite');
      expect(storeMock.clear).toHaveBeenCalled();
      expect(storeMock.put).toHaveBeenCalledTimes(2);
      expect(dbMock.close).toHaveBeenCalled();
    });
  });

  describe('状態管理', () => {
    it('トランザクション処理中はisTransactionPendingがtrueになる', async () => {
      const { result } = await getHook([]);

      act(() => {
        result.current.addTeamsChats(createTeamsChatsItem());
      });

      expect(result.current.isTransactionPending).toBe(true);
    });

    it('アンマウント時にisUnmountedフラグが設定される', async () => {
      const { result, unmount } = await getHook([]);

      unmount();

      // アンマウント後の処理は内部的に制御されるため、直接テストは困難
      // ここでは正常にアンマウントできることを確認
      expect(result.current).toBeDefined();
    });
  });
});
